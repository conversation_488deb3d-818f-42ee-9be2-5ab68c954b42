import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/user_main_layout.dart';
import '../models/asset.dart';
import '../services/api_service.dart';

class UserAssetDetailScreen extends StatefulWidget {
  final String assetId;

  const UserAssetDetailScreen({
    super.key,
    required this.assetId,
  });

  @override
  State<UserAssetDetailScreen> createState() => _UserAssetDetailScreenState();
}

class _UserAssetDetailScreenState extends State<UserAssetDetailScreen> {
  final ApiService _apiService = ApiService();
  Asset? _asset;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAsset();
  }

  Future<void> _loadAsset() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final asset = await _apiService.getAsset(widget.assetId);
      
      if (mounted) {
        setState(() {
          _asset = asset;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return UserMainLayout(
      currentRoute: '/user-assets/${widget.assetId}',
      title: '资产详情',
      showBackButton: true,
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAsset,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_asset == null) {
      return const Center(
        child: Text('资产不存在'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAssetHeader(),
          const SizedBox(height: 20),
          _buildBasicInfo(),
          const SizedBox(height: 20),
          _buildTechnicalInfo(),
          const SizedBox(height: 20),
          _buildLocationInfo(),
          const SizedBox(height: 20),
          _buildStatusInfo(),
        ],
      ),
    );
  }

  Widget _buildAssetHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getCategoryColor(_asset!.category),
            _getCategoryColor(_asset!.category).withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: _getCategoryColor(_asset!.category).withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getCategoryIcon(_asset!.category),
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _asset!.name,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _asset!.assetNumber,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusBadge(),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _getCategoryDisplayName(_asset!.category),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color color;
    String label;
    
    switch (_asset!.status) {
      case AssetStatus.available:
        color = Colors.green;
        label = '可用';
        break;
      case AssetStatus.assigned:
        color = Colors.blue;
        label = '已分配';
        break;
      case AssetStatus.maintenance:
        color = Colors.orange;
        label = '维护中';
        break;
      case AssetStatus.retired:
        color = Colors.grey;
        label = '已报废';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return _buildInfoCard(
      title: '基本信息',
      icon: Icons.info_outline,
      children: [
        _buildInfoRow('资产名称', _asset!.name),
        _buildInfoRow('资产编号', _asset!.assetNumber),
        if (_asset!.serialNumber != null && _asset!.serialNumber!.isNotEmpty)
          _buildInfoRow('序列号', _asset!.serialNumber!),
        if (_asset!.brand != null && _asset!.brand!.isNotEmpty)
          _buildInfoRow('品牌', _asset!.brand!),
        if (_asset!.model != null && _asset!.model!.isNotEmpty)
          _buildInfoRow('型号', _asset!.model!),
      ],
    );
  }

  Widget _buildTechnicalInfo() {
    return _buildInfoCard(
      title: '技术信息',
      icon: Icons.settings,
      children: [
        _buildInfoRow('类别', _getCategoryDisplayName(_asset!.category)),
        _buildInfoRow('状态', _getStatusDisplayName(_asset!.status)),
        if (_asset!.purchaseDate != null)
          _buildInfoRow('采购日期', _formatDate(_asset!.purchaseDate!)),
        if (_asset!.assignedTo != null && _asset!.assignedTo!.isNotEmpty)
          _buildInfoRow('分配给', _asset!.assignedTo!),
      ],
    );
  }

  Widget _buildLocationInfo() {
    return _buildInfoCard(
      title: '位置信息',
      icon: Icons.location_on,
      children: [
        _buildInfoRow('位置', _asset!.location ?? '未指定'),
      ],
    );
  }

  Widget _buildStatusInfo() {
    return _buildInfoCard(
      title: '状态信息',
      icon: Icons.timeline,
      children: [
        _buildInfoRow('创建时间', _formatDateTime(_asset!.createdAt)),
        if (_asset!.updatedAt != null)
          _buildInfoRow('更新时间', _formatDateTime(_asset!.updatedAt!)),
      ],
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Colors.blue;
      case AssetCategory.desktop:
        return Colors.indigo;
      case AssetCategory.monitor:
        return Colors.purple;
      case AssetCategory.printer:
        return Colors.green;
      case AssetCategory.phone:
        return Colors.orange;
      case AssetCategory.tablet:
        return Colors.teal;
      case AssetCategory.server:
        return Colors.red;
      case AssetCategory.network:
        return Colors.cyan;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Icons.laptop;
      case AssetCategory.desktop:
        return Icons.computer;
      case AssetCategory.monitor:
        return Icons.monitor;
      case AssetCategory.printer:
        return Icons.print;
      case AssetCategory.phone:
        return Icons.phone_android;
      case AssetCategory.tablet:
        return Icons.tablet;
      case AssetCategory.server:
        return Icons.dns;
      case AssetCategory.network:
        return Icons.router;
      case AssetCategory.other:
        return Icons.category;
    }
  }

  String _getCategoryDisplayName(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return '笔记本电脑';
      case AssetCategory.desktop:
        return '台式电脑';
      case AssetCategory.monitor:
        return '显示器';
      case AssetCategory.printer:
        return '打印机';
      case AssetCategory.phone:
        return '手机';
      case AssetCategory.tablet:
        return '平板电脑';
      case AssetCategory.server:
        return '服务器';
      case AssetCategory.network:
        return '网络设备';
      case AssetCategory.other:
        return '其他';
    }
  }

  String _getStatusDisplayName(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return '可用';
      case AssetStatus.assigned:
        return '已分配';
      case AssetStatus.maintenance:
        return '维护中';
      case AssetStatus.retired:
        return '已报废';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
