import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../screens/login_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/asset_list_screen.dart';
import '../screens/asset_detail_screen.dart';
import '../screens/asset_form_screen.dart';
import '../screens/activity_log_screen.dart';
import '../screens/activity_log_test_screen.dart';
import '../screens/splash_screen.dart';
import '../screens/user_management_screen.dart';
import '../screens/user_form_screen.dart';
import '../screens/user_detail_screen.dart';
import '../screens/user_dashboard_screen.dart';
import '../screens/user_profile_screen.dart';
import '../screens/ticket_list_screen.dart';
import '../screens/ticket_create_screen.dart';
import '../screens/ticket_detail_screen.dart';
import '../screens/user_assets_screen.dart';
import '../models/user.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';

  // 管理员路由
  static const String dashboard = '/dashboard';
  static const String assetList = '/assets';
  static const String assetDetail = '/assets/:id';
  static const String assetForm = '/assets/new';
  static const String assetCreate = '/assets/new';
  static const String assetEdit = '/assets/:id/edit';
  static const String activityLog = '/activity-logs';
  static const String activityLogTest = '/activity-logs-test';
  static const String userList = '/users';
  static const String userDetail = '/users/:id';
  static const String userCreate = '/users/new';
  static const String userEdit = '/users/:id/edit';

  // 普通用户路由
  static const String userDashboard = '/user-dashboard';
  static const String userAssets = '/user-assets';
  static const String userProfile = '/user-profile';
  static const String ticketList = '/tickets';
  static const String ticketCreate = '/tickets/new';
  static const String ticketDetail = '/tickets/:id';
}

GoRouter createAppRouter(AuthProvider authProvider) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    refreshListenable: authProvider,
    redirect: (context, state) {
      final isLoggedIn = authProvider.isLoggedIn;
      final isLoading = authProvider.isLoading;
      final currentUser = authProvider.currentUser;

      // 如果正在加载，显示启动页
      if (isLoading) {
        return AppRoutes.splash;
      }

      // 如果在启动页且已登录，根据用户角色跳转到对应仪表板
      if (state.matchedLocation == AppRoutes.splash && isLoggedIn) {
        if (currentUser?.isAdmin == true) {
          return AppRoutes.dashboard;
        } else {
          return AppRoutes.userDashboard;
        }
      }

      // 如果在启动页且未登录，跳转到登录页
      if (state.matchedLocation == AppRoutes.splash && !isLoggedIn) {
        return AppRoutes.login;
      }

      // 如果未登录且不在登录页，跳转到登录页
      if (!isLoggedIn && state.matchedLocation != AppRoutes.login) {
        return AppRoutes.login;
      }

      // 如果已登录且在登录页，根据用户角色跳转到对应仪表板
      if (isLoggedIn && state.matchedLocation == AppRoutes.login) {
        if (currentUser?.isAdmin == true) {
          return AppRoutes.dashboard;
        } else {
          return AppRoutes.userDashboard;
        }
      }

      // 权限检查：普通用户不能访问管理员页面
      if (isLoggedIn && currentUser?.isNormalUser == true) {
        final adminRoutes = [
          AppRoutes.dashboard,
          AppRoutes.assetList,
          AppRoutes.activityLog,
          AppRoutes.userList,
        ];

        if (adminRoutes.any((route) => state.matchedLocation.startsWith(route.replaceAll(':id', '')))) {
          return AppRoutes.userDashboard;
        }
      }

      return null; // 不需要重定向
    },
    routes: [
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.dashboard,
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),
      GoRoute(
        path: AppRoutes.assetList,
        name: 'assetList',
        builder: (context, state) => const AssetListScreen(),
      ),
      GoRoute(
        path: AppRoutes.assetCreate,
        name: 'assetCreate',
        builder: (context, state) {
          print('=== ROUTE MATCHED: assetCreate ===');
          print('Path: ${state.matchedLocation}');
          return const AssetFormScreen();
        },
      ),
      GoRoute(
        path: AppRoutes.assetEdit,
        name: 'assetEdit',
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          print('=== ROUTE MATCHED: assetEdit ===');
          print('Path: ${state.matchedLocation}');
          print('Asset ID: $id');
          return AssetFormScreen(assetId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.assetDetail,
        name: 'assetDetail',
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          print('=== ROUTE MATCHED: assetDetail ===');
          print('Path: ${state.matchedLocation}');
          print('Asset ID: $id');
          return AssetDetailScreen(assetId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.activityLog,
        name: 'activityLog',
        builder: (context, state) => const ActivityLogScreen(),
      ),
      GoRoute(
        path: AppRoutes.activityLogTest,
        name: 'activityLogTest',
        builder: (context, state) => const ActivityLogTestScreen(),
      ),
      GoRoute(
        path: AppRoutes.userList,
        name: 'userList',
        builder: (context, state) => const UserManagementScreen(),
      ),
      GoRoute(
        path: AppRoutes.userCreate,
        name: 'userCreate',
        builder: (context, state) => const UserFormScreen(),
      ),
      GoRoute(
        path: AppRoutes.userEdit,
        name: 'userEdit',
        builder: (context, state) {
          final id = int.parse(state.pathParameters['id']!);
          // Note: We'll need to fetch the user data or pass it through state
          return UserFormScreen(user: null); // This will need to be implemented properly
        },
      ),
      GoRoute(
        path: AppRoutes.userDetail,
        name: 'userDetail',
        builder: (context, state) {
          final id = int.parse(state.pathParameters['id']!);
          // Note: We'll need to fetch the user data or pass it through state
          return UserDetailScreen(user: User(
            id: id,
            username: '',
            email: '',
            role: '',
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            assignedAssetsCount: 0,
          )); // This will need to be implemented properly
        },
      ),

      // 普通用户路由
      GoRoute(
        path: AppRoutes.userDashboard,
        name: 'userDashboard',
        builder: (context, state) => const UserDashboardScreen(),
      ),
      GoRoute(
        path: AppRoutes.userProfile,
        name: 'userProfile',
        builder: (context, state) => const UserProfileScreen(),
      ),
      GoRoute(
        path: AppRoutes.userAssets,
        name: 'userAssets',
        builder: (context, state) => const UserAssetsScreen(),
      ),
      GoRoute(
        path: AppRoutes.ticketList,
        name: 'ticketList',
        builder: (context, state) => const TicketListScreen(),
      ),
      GoRoute(
        path: AppRoutes.ticketCreate,
        name: 'ticketCreate',
        builder: (context, state) => const TicketCreateScreen(),
      ),
      GoRoute(
        path: AppRoutes.ticketDetail,
        name: 'ticketDetail',
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return TicketDetailScreen(ticketId: id);
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '页面未找到',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              '路径: ${state.matchedLocation}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.dashboard),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
  );
}

// 保持向后兼容
final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.splash,
  routes: [],
);
