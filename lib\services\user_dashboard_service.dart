import '../models/user_dashboard_stats.dart';
import 'api_service.dart';

class UserDashboardService {
  final ApiService _apiService = ApiService();

  Future<UserDashboardStats> getUserDashboardStats() async {
    try {
      await _apiService.initializeToken();
      
      // 暂时使用模拟数据，后续会实现真实的API
      // TODO: 实现真实的用户仪表盘统计API
      return _getMockUserDashboardStats();
      
      // 真实API调用（待后端实现）
      // final response = await _apiService.dio.get('/user/dashboard/stats');
      // return UserDashboardStats.fromJson(response.data);
    } catch (e) {
      throw Exception('获取用户仪表盘数据失败: $e');
    }
  }

  // 模拟数据，用于开发测试
  UserDashboardStats _getMockUserDashboardStats() {
    return UserDashboardStats(
      assignedToMeCount: 5,
      activeAssetsCount: 4,
      maintenanceAssetsCount: 1,
      pendingTicketsCount: 2,
      completedTicketsCount: 8,
      recentAssets: [
        UserAssetSummary(
          id: 1,
          name: 'MacBook Pro 16"',
          assetNumber: 'LAP-001',
          category: 'Laptop',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 30)),
        ),
        UserAssetSummary(
          id: 2,
          name: 'iPhone 14 Pro',
          assetNumber: 'MOB-001',
          category: 'Mobile',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 15)),
        ),
        UserAssetSummary(
          id: 3,
          name: 'Dell Monitor 27"',
          assetNumber: 'MON-001',
          category: 'Monitor',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 45)),
        ),
      ],
      recentTickets: [
        UserTicketSummary(
          id: 1,
          title: '电脑运行缓慢',
          status: 'In Progress',
          priority: 'Medium',
          createdDate: DateTime.now().subtract(const Duration(days: 2)),
          updatedDate: DateTime.now().subtract(const Duration(hours: 6)),
        ),
        UserTicketSummary(
          id: 2,
          title: '软件安装请求',
          status: 'Pending',
          priority: 'Low',
          createdDate: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ],
    );
  }

  Future<List<UserAssetSummary>> getUserAssets() async {
    try {
      await _apiService.initializeToken();
      
      // 暂时使用模拟数据
      // TODO: 实现真实的用户资产列表API
      return _getMockUserDashboardStats().recentAssets;
      
      // 真实API调用（待后端实现）
      // final response = await _apiService.dio.get('/user/assets');
      // final List<dynamic> data = response.data;
      // return data.map((json) => UserAssetSummary.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取用户资产列表失败: $e');
    }
  }

  Future<List<UserTicketSummary>> getUserTickets() async {
    try {
      await _apiService.initializeToken();
      
      // 暂时使用模拟数据
      // TODO: 实现真实的用户工单列表API
      return _getMockUserDashboardStats().recentTickets;
      
      // 真实API调用（待后端实现）
      // final response = await _apiService.dio.get('/user/tickets');
      // final List<dynamic> data = response.data;
      // return data.map((json) => UserTicketSummary.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取用户工单列表失败: $e');
    }
  }
}
