import '../models/user_dashboard_stats.dart';
import '../models/asset.dart';
import 'api_service.dart';

class UserDashboardService {
  final ApiService _apiService = ApiService();

  Future<UserDashboardStats> getUserDashboardStats() async {
    try {
      await _apiService.initializeToken();

      // 尝试获取真实数据
      try {
        // 获取当前用户信息
        final currentUserResponse = await _apiService.dio.get('/auth/me');
        final currentUserId = currentUserResponse.data['id'];

        // 获取用户资产统计
        final statsResponse = await _apiService.dio.get('/assets/user/$currentUserId/stats');
        final assetStats = statsResponse.data;

        // 获取用户最近的资产（前3个）
        final assetsResponse = await _apiService.dio.get('/assets/user/$currentUserId?page=1&limit=3');
        final List<dynamic> assetsData = assetsResponse.data['assets'] ?? [];
        final recentAssets = assetsData.map((json) => UserAssetSummary.fromAsset(Asset.fromJson(json))).toList();

        return UserDashboardStats(
          assignedToMeCount: assetStats['assignedToMeCount'] ?? 0,
          activeAssetsCount: assetStats['activeAssetsCount'] ?? 0,
          maintenanceAssetsCount: assetStats['maintenanceAssetsCount'] ?? 0,
          pendingTicketsCount: 0, // TODO: 实现工单统计
          completedTicketsCount: 0, // TODO: 实现工单统计
          recentAssets: recentAssets,
          recentTickets: [], // TODO: 实现最近工单
        );
      } catch (apiError) {
        print('API调用失败，使用模拟数据: $apiError');
        // 如果API调用失败，回退到模拟数据
        return _getMockUserDashboardStats();
      }
    } catch (e) {
      throw Exception('获取用户仪表盘数据失败: $e');
    }
  }

  // 模拟数据，用于开发测试
  UserDashboardStats _getMockUserDashboardStats() {
    return UserDashboardStats(
      assignedToMeCount: 5,
      activeAssetsCount: 4,
      maintenanceAssetsCount: 1,
      pendingTicketsCount: 2,
      completedTicketsCount: 8,
      recentAssets: [
        UserAssetSummary(
          id: 1,
          name: 'MacBook Pro 16"',
          assetNumber: 'LAP-001',
          category: 'Laptop',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 30)),
        ),
        UserAssetSummary(
          id: 2,
          name: 'iPhone 14 Pro',
          assetNumber: 'MOB-001',
          category: 'Mobile',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 15)),
        ),
        UserAssetSummary(
          id: 3,
          name: 'Dell Monitor 27"',
          assetNumber: 'MON-001',
          category: 'Monitor',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 45)),
        ),
      ],
      recentTickets: [
        UserTicketSummary(
          id: 1,
          title: '电脑运行缓慢',
          status: 'In Progress',
          priority: 'Medium',
          createdDate: DateTime.now().subtract(const Duration(days: 2)),
          updatedDate: DateTime.now().subtract(const Duration(hours: 6)),
        ),
        UserTicketSummary(
          id: 2,
          title: '软件安装请求',
          status: 'Pending',
          priority: 'Low',
          createdDate: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ],
    );
  }

  Future<List<Asset>> getUserAssets({
    int page = 1,
    int limit = 20,
    String? search,
    List<String>? categories,
    List<String>? statuses,
  }) async {
    try {
      await _apiService.initializeToken();

      // 尝试获取真实数据
      try {
        // 获取当前用户信息
        final currentUserResponse = await _apiService.dio.get('/auth/me');
        final currentUserId = currentUserResponse.data['id'];

        // 构建查询参数
        final queryParts = <String>[];
        queryParts.add('page=$page');
        queryParts.add('limit=$limit');

        if (search != null && search.isNotEmpty) {
          queryParts.add('search=${Uri.encodeComponent(search)}');
        }

        if (categories != null && categories.isNotEmpty) {
          for (final category in categories) {
            queryParts.add('category=${Uri.encodeComponent(category)}');
          }
        }

        if (statuses != null && statuses.isNotEmpty) {
          for (final status in statuses) {
            queryParts.add('status=${Uri.encodeComponent(status)}');
          }
        }

        final queryString = queryParts.join('&');
        final url = '/assets/user/$currentUserId?$queryString';

        final response = await _apiService.dio.get(url);
        final List<dynamic> data = response.data['assets'] ?? [];
        return data.map((json) => Asset.fromJson(json)).toList();
      } catch (apiError) {
        print('用户资产API调用失败，使用模拟数据: $apiError');
        // 如果API调用失败，返回模拟的用户资产数据
        return _getMockUserAssets(search: search, categories: categories, statuses: statuses);
      }
    } catch (e) {
      throw Exception('获取用户资产列表失败: $e');
    }
  }

  Future<List<UserTicketSummary>> getUserTickets() async {
    try {
      await _apiService.initializeToken();
      
      // 暂时使用模拟数据
      // TODO: 实现真实的用户工单列表API
      return _getMockUserDashboardStats().recentTickets;
      
      // 真实API调用（待后端实现）
      // final response = await _apiService.dio.get('/user/tickets');
      // final List<dynamic> data = response.data;
      // return data.map((json) => UserTicketSummary.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取用户工单列表失败: $e');
    }
  }
}
