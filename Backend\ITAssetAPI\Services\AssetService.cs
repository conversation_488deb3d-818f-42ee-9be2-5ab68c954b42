using Microsoft.EntityFrameworkCore;
using ITAssetAPI.Data;
using ITAssetAPI.DTOs;
using ITAssetAPI.Models;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using System.Text.Json;

namespace ITAssetAPI.Services
{
    public class AssetService : IAssetService
    {
        private readonly ApplicationDbContext _context;
        private readonly IActivityLogService _activityLogService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        
        // 资产类别前缀映射
        private readonly Dictionary<AssetCategory, string> _categoryPrefixes = new()
        {
            { AssetCategory.Laptop, "NB" },      // 笔记本电脑
            { AssetCategory.Desktop, "PC" },     // 台式电脑
            { AssetCategory.Monitor, "MON" },    // 显示器
            { AssetCategory.Printer, "PRT" },    // 打印机
            { AssetCategory.Phone, "PHN" },      // 电话
            { AssetCategory.Tablet, "TAB" },     // 平板
            { AssetCategory.Server, "SVR" },     // 服务器
            { AssetCategory.Network, "NET" },    // 网络设备
            { AssetCategory.Other, "OTH" }       // 其他
        };

        public AssetService(ApplicationDbContext context, IActivityLogService activityLogService, IHttpContextAccessor httpContextAccessor)
        {
            _context = context;
            _activityLogService = activityLogService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<string> GenerateNextAssetNumberAsync(AssetCategory category)
        {
            // 获取类别前缀
            var prefix = _categoryPrefixes.GetValueOrDefault(category, "AST");
            
            // 查询该类别下的资产数量
            var count = await _context.Assets
                .Where(a => a.Category == category)
                .CountAsync();
            
            // 生成下一个编号：前缀 + 年份 + 三位序号
            var year = DateTime.UtcNow.Year;
            var nextNumber = count + 1;
            
            return $"{prefix}{year}{nextNumber:D3}";
        }

        public async Task<AssetListResponseDto> GetAssetsAsync(int page = 1, int pageSize = 20, string? search = null, string[]? categories = null, string[]? statuses = null)
        {
            var query = _context.Assets.AsQueryable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(search))
            {
                query = query.Where(a =>
                    a.Name.Contains(search) ||
                    a.AssetNumber.Contains(search) ||
                    (a.SerialNumber != null && a.SerialNumber.Contains(search)));
            }

            // Apply categories filter
            if (categories != null && categories.Length > 0)
            {
                Console.WriteLine($"=== CATEGORIES FILTER DEBUG ===");
                Console.WriteLine($"Categories received: [{string.Join(", ", categories)}]");
                Console.WriteLine($"Categories length: {categories.Length}");

                var validCategories = new List<AssetCategory>();
                foreach (var category in categories)
                {
                    Console.WriteLine($"Processing category: '{category}'");
                    if (!string.IsNullOrWhiteSpace(category) && Enum.TryParse<AssetCategory>(category, true, out var categoryEnum))
                    {
                        validCategories.Add(categoryEnum);
                        Console.WriteLine($"Added valid category: {categoryEnum}");
                    }
                    else
                    {
                        Console.WriteLine($"Invalid category: '{category}'");
                    }
                }

                Console.WriteLine($"Valid categories count: {validCategories.Count}");
                if (validCategories.Count > 0)
                {
                    Console.WriteLine($"Applying category filter: [{string.Join(", ", validCategories)}]");
                    query = query.Where(a => validCategories.Contains(a.Category));
                }
            }

            // Apply statuses filter
            if (statuses != null && statuses.Length > 0)
            {
                Console.WriteLine($"=== STATUSES FILTER DEBUG ===");
                Console.WriteLine($"Statuses received: [{string.Join(", ", statuses)}]");
                Console.WriteLine($"Statuses length: {statuses.Length}");

                var validStatuses = new List<AssetStatus>();
                foreach (var status in statuses)
                {
                    Console.WriteLine($"Processing status: '{status}'");
                    if (!string.IsNullOrWhiteSpace(status) && Enum.TryParse<AssetStatus>(status, true, out var statusEnum))
                    {
                        validStatuses.Add(statusEnum);
                        Console.WriteLine($"Added valid status: {statusEnum}");
                    }
                    else
                    {
                        Console.WriteLine($"Invalid status: '{status}'");
                    }
                }

                Console.WriteLine($"Valid statuses count: {validStatuses.Count}");
                if (validStatuses.Count > 0)
                {
                    Console.WriteLine($"Applying status filter: [{string.Join(", ", validStatuses)}]");
                    query = query.Where(a => validStatuses.Contains(a.Status));
                }
            }

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            var assets = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(a => MapToAssetDto(a))
                .ToListAsync();

            return new AssetListResponseDto
            {
                Assets = assets,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = totalPages
            };
        }

        public async Task<AssetDto?> GetAssetByIdAsync(int id)
        {
            var asset = await _context.Assets.FindAsync(id);
            return asset != null ? MapToAssetDto(asset) : null;
        }

        public async Task<AssetDto> CreateAssetAsync(CreateAssetDto createAssetDto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // 如果没有提供资产编号，自动生成
                var assetNumber = string.IsNullOrWhiteSpace(createAssetDto.AssetNumber) 
                    ? await GenerateNextAssetNumberAsync(createAssetDto.Category)
                    : createAssetDto.AssetNumber;

                // 处理用户分配
                string? assignedToName = string.IsNullOrWhiteSpace(createAssetDto.AssignedTo) ? null : createAssetDto.AssignedTo;
                int? assignedUserId = createAssetDto.AssignedUserId.HasValue && createAssetDto.AssignedUserId.Value > 0 
                    ? createAssetDto.AssignedUserId 
                    : null;
                
                // 如果分配了用户，自动设置状态为已分配
                var status = createAssetDto.Status;
                if (assignedUserId.HasValue && assignedUserId.Value > 0)
                {
                    status = AssetStatus.Assigned;
                }

                var asset = new Asset
                {
                    Name = createAssetDto.Name,
                    AssetNumber = assetNumber,
                    Category = createAssetDto.Category,
                    Status = status,
                    Brand = createAssetDto.Brand,
                    Model = createAssetDto.Model,
                    SerialNumber = createAssetDto.SerialNumber,
                    AssignedTo = assignedToName,
                    AssignedUserId = assignedUserId,
                    PurchaseDate = createAssetDto.PurchaseDate,
                    Value = createAssetDto.Value,
                    Vendor = createAssetDto.Vendor,
                    Description = createAssetDto.Description,
                    Location = createAssetDto.Location,
                    LastMaintenanceDate = createAssetDto.LastMaintenanceDate,
                    NextMaintenanceDate = createAssetDto.NextMaintenanceDate
                };

                _context.Assets.Add(asset);
                await _context.SaveChangesAsync();

                // 获取当前用户信息
                var (userId, userName) = GetCurrentUserInfo();
                
                // 创建资产后的值（用于活动日志）
                var newValues = new Dictionary<string, object>
                {
                    { "Name", asset.Name },
                    { "Category", asset.Category.ToString() },
                    { "Status", asset.Status.ToString() },
                    { "Brand", asset.Brand ?? "" },
                    { "Model", asset.Model ?? "" },
                    { "SerialNumber", asset.SerialNumber ?? "" },
                    { "AssignedTo", asset.AssignedTo ?? "" },
                    { "AssignedUserId", asset.AssignedUserId?.ToString() ?? "" },
                    { "Location", asset.Location ?? "" },
                    { "Value", asset.Value?.ToString() ?? "" },
                    { "Description", asset.Description ?? "" }
                };

                Console.WriteLine($"=== CREATE ASSET DEBUG ===");
                Console.WriteLine($"Asset: {asset.Name} ({asset.AssetNumber})");
                Console.WriteLine($"AssignedTo: '{asset.AssignedTo}'");
                Console.WriteLine($"AssignedUserId: {asset.AssignedUserId}");
                Console.WriteLine($"Status: {asset.Status}");
                Console.WriteLine($"NewValues AssignedTo: '{newValues["AssignedTo"]}'");
                Console.WriteLine($"NewValues AssignedUserId: '{newValues["AssignedUserId"]}'");

                // 确定活动类型
                var activityTypes = new List<ActivityType>();
                var primaryType = ActivityType.Create; // 默认主类型为创建
                
                // 如果资产被分配给了用户，添加分配活动类型
                if (!string.IsNullOrWhiteSpace(asset.AssignedTo) || asset.AssignedUserId.HasValue)
                {
                    activityTypes.Add(ActivityType.Assign);
                    Console.WriteLine($"Added Assign activity type");
                }
                
                // 如果资产状态为维护中或已报废，添加相应活动类型
                if (asset.Status == AssetStatus.Maintenance)
                {
                    activityTypes.Add(ActivityType.Maintenance);
                }
                else if (asset.Status == AssetStatus.Retired)
                {
                    activityTypes.Add(ActivityType.Dispose);
                }
                
                // 记录组合活动日志
                await _activityLogService.LogCombinedAssetActivityAsync(
                    primaryType,
                    activityTypes,
                    asset.Id,
                    userId,
                    userName,
                    asset.Name,
                    asset.AssetNumber,
                    null, // 创建时没有旧值
                    newValues
                );
                
                Console.WriteLine($"Successfully logged asset creation: {asset.Name} ({asset.AssetNumber}) by user {userName} (ID: {userId})");
                
                await transaction.CommitAsync();
                
                return MapToAssetDto(asset);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                
                Console.WriteLine($"Failed to create asset: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                throw;
            }
        }

        public async Task<AssetDto?> UpdateAssetAsync(int id, UpdateAssetDto updateAssetDto)
        {
            // 使用事务确保操作的原子性
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var asset = await _context.Assets.FindAsync(id);
                if (asset == null)
                {
                    return null;
                }

                // 保存更新前的资产状态（用于活动日志）
                var oldValues = new Dictionary<string, object>
                {
                    { "Name", asset.Name },
                    { "Category", asset.Category.ToString() },
                    { "Status", asset.Status.ToString() },
                    { "Brand", asset.Brand ?? "" },
                    { "Model", asset.Model ?? "" },
                    { "SerialNumber", asset.SerialNumber ?? "" },
                    { "AssignedTo", asset.AssignedTo ?? "" },
                    { "AssignedUserId", asset.AssignedUserId?.ToString() ?? "" },
                    { "Location", asset.Location ?? "" },
                    { "Value", asset.Value?.ToString() ?? "" },
                    { "Description", asset.Description ?? "" }
                };

                // 记录原始分配状态
                var originalAssignedTo = asset.AssignedTo;
                var originalAssignedUserId = asset.AssignedUserId;
                var originalStatus = asset.Status;
                var (userId, userName) = GetCurrentUserInfo();
                
                Console.WriteLine($"=== UPDATE ASSET {id} ===");
                Console.WriteLine($"Original: AssignedUserId={originalAssignedUserId}, AssignedTo='{originalAssignedTo}', Status='{originalStatus}'");
                Console.WriteLine($"Request: AssignedUserId={updateAssetDto.AssignedUserId}, AssignedTo='{updateAssetDto.AssignedTo}', Status='{updateAssetDto.Status}'");

                // Update only non-null properties
                if (!string.IsNullOrWhiteSpace(updateAssetDto.Name))
                    asset.Name = updateAssetDto.Name;
                
                if (updateAssetDto.Category.HasValue)
                    asset.Category = updateAssetDto.Category.Value;
                
                if (updateAssetDto.Status.HasValue)
                    asset.Status = updateAssetDto.Status.Value;
                
                if (updateAssetDto.Brand != null)
                    asset.Brand = updateAssetDto.Brand;
                
                if (updateAssetDto.Model != null)
                    asset.Model = updateAssetDto.Model;
                
                if (updateAssetDto.SerialNumber != null)
                    asset.SerialNumber = updateAssetDto.SerialNumber;
                
                // 处理分配信息更新，确保AssignedTo和AssignedUserId保持同步
                bool assignmentProcessed = false;
                if (updateAssetDto.AssignedUserId.HasValue)
                {
                    if (updateAssetDto.AssignedUserId.Value == 0)
                    {
                        // 取消分配 - 强制清空所有分配相关字段
                        asset.AssignedUserId = null;
                        asset.AssignedTo = null;
                        if (asset.Status == AssetStatus.Assigned)
                        {
                            asset.Status = AssetStatus.Available;
                        }
                        Console.WriteLine($"Asset {asset.Id} assignment cleared: AssignedUserId={asset.AssignedUserId}, AssignedTo={asset.AssignedTo}");
                    }
                    else
                    {
                        // 分配给用户
                        asset.AssignedUserId = updateAssetDto.AssignedUserId;
                        // 如果提供了AssignedTo，使用它；否则保持现有值
                        if (updateAssetDto.AssignedTo != null)
                        {
                            asset.AssignedTo = string.IsNullOrWhiteSpace(updateAssetDto.AssignedTo) ? null : updateAssetDto.AssignedTo;
                        }
                        asset.Status = AssetStatus.Assigned;
                    }
                    assignmentProcessed = true;
                }
                
                // 只有在没有处理AssignedUserId的情况下才处理AssignedTo（向后兼容）
                if (!assignmentProcessed && updateAssetDto.AssignedTo != null)
                {
                    asset.AssignedTo = string.IsNullOrWhiteSpace(updateAssetDto.AssignedTo) ? null : updateAssetDto.AssignedTo;
                    if (string.IsNullOrWhiteSpace(updateAssetDto.AssignedTo))
                    {
                        asset.AssignedUserId = null;
                        if (asset.Status == AssetStatus.Assigned)
                        {
                            asset.Status = AssetStatus.Available;
                        }
                    }
                }
                
                if (updateAssetDto.PurchaseDate.HasValue)
                    asset.PurchaseDate = updateAssetDto.PurchaseDate;
                
                if (updateAssetDto.Value.HasValue)
                    asset.Value = updateAssetDto.Value;
                
                if (updateAssetDto.Vendor != null)
                    asset.Vendor = updateAssetDto.Vendor;
                
                if (updateAssetDto.Description != null)
                    asset.Description = updateAssetDto.Description;
                
                if (updateAssetDto.Location != null)
                    asset.Location = updateAssetDto.Location;
                
                if (updateAssetDto.LastMaintenanceDate.HasValue)
                    asset.LastMaintenanceDate = updateAssetDto.LastMaintenanceDate;
                
                if (updateAssetDto.NextMaintenanceDate.HasValue)
                    asset.NextMaintenanceDate = updateAssetDto.NextMaintenanceDate;

                asset.UpdatedAt = DateTime.UtcNow;
                
                Console.WriteLine($"Final: AssignedUserId={asset.AssignedUserId}, AssignedTo='{asset.AssignedTo}', Status={asset.Status}");

                // 保存对资产的更改
                await _context.SaveChangesAsync();

                // 准备更新后的值（用于活动日志）
                var newValues = new Dictionary<string, object>
                {
                    { "Name", asset.Name },
                    { "Category", asset.Category.ToString() },
                    { "Status", asset.Status.ToString() },
                    { "Brand", asset.Brand ?? "" },
                    { "Model", asset.Model ?? "" },
                    { "SerialNumber", asset.SerialNumber ?? "" },
                    { "AssignedTo", asset.AssignedTo ?? "" },
                    { "AssignedUserId", asset.AssignedUserId?.ToString() ?? "" },
                    { "Location", asset.Location ?? "" },
                    { "Value", asset.Value?.ToString() ?? "" },
                    { "Description", asset.Description ?? "" }
                };

                // 确定发生了哪些活动类型
                var activityTypes = new List<ActivityType>();
                var primaryType = ActivityType.Update; // 默认主类型为更新
                
                // 检查分配状态变化
                if (originalAssignedUserId != asset.AssignedUserId)
                {
                    if (!originalAssignedUserId.HasValue && asset.AssignedUserId.HasValue)
                    {
                        // 从未分配变为分配给某人
                        primaryType = ActivityType.Assign;
                    }
                    else if (originalAssignedUserId.HasValue && !asset.AssignedUserId.HasValue)
                    {
                        // 从分配给某人变为未分配
                        primaryType = ActivityType.Unassign;
                    }
                    else if (originalAssignedUserId.HasValue && asset.AssignedUserId.HasValue && originalAssignedUserId != asset.AssignedUserId)
                    {
                        // 从分配给某人变为分配给另一个人（视为重新分配）
                        primaryType = ActivityType.Assign;
                    }
                }
                
                // 检查状态变化（维护或报废）
                else if (originalStatus != asset.Status)
                {
                    if (asset.Status == AssetStatus.Maintenance)
                    {
                        primaryType = ActivityType.Maintenance;
                    }
                    else if (asset.Status == AssetStatus.Retired)
                    {
                        primaryType = ActivityType.Dispose;
                    }
                }
                
                // 如果有主要活动类型变化，且还有其他字段变化，则添加Update作为次要类型
                if (primaryType != ActivityType.Update)
                {
                    // 检查是否有其他字段变化
                    bool hasOtherChanges = false;
                    foreach (var kvp in oldValues)
                    {
                        if (kvp.Key != "AssignedTo" && kvp.Key != "AssignedUserId" && kvp.Key != "Status")
                        {
                            var oldVal = kvp.Value?.ToString() ?? "";
                            var newVal = newValues[kvp.Key]?.ToString() ?? "";
                            if (oldVal != newVal)
                            {
                                hasOtherChanges = true;
                                break;
                            }
                        }
                    }
                    
                    if (hasOtherChanges)
                    {
                        activityTypes.Add(ActivityType.Update);
                    }
                }
                
                // 记录单一活动日志（避免重复记录）
                await _activityLogService.LogCombinedAssetActivityAsync(
                    primaryType,
                    activityTypes,
                    asset.Id,
                    userId,
                    userName,
                    asset.Name,
                    asset.AssetNumber,
                    oldValues,
                    newValues
                );
                
                Console.WriteLine($"Successfully logged combined asset activity: {asset.Name} ({asset.AssetNumber}) by user {userName} (ID: {userId})");
                
                // 提交事务
                await transaction.CommitAsync();
                
                return MapToAssetDto(asset);
            }
            catch (Exception ex)
            {
                // 回滚事务
                await transaction.RollbackAsync();
                
                Console.WriteLine($"Failed to update asset: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                throw; // 重新抛出异常以便控制器处理
            }
        }

        public async Task<bool> DeleteAssetAsync(int id)
        {
            var asset = await _context.Assets.FindAsync(id);
            if (asset == null)
            {
                return false;
            }

            // 记录删除前的信息
            var assetName = asset.Name;
            var assetNumber = asset.AssetNumber;
            var (userId, userName) = GetCurrentUserInfo();
            
            // 删除前的资产状态（用于活动日志）
            var oldValues = new Dictionary<string, object>
            {
                { "Name", asset.Name },
                { "Category", asset.Category.ToString() },
                { "Status", asset.Status.ToString() },
                { "Brand", asset.Brand ?? "" },
                { "Model", asset.Model ?? "" },
                { "SerialNumber", asset.SerialNumber ?? "" },
                { "AssignedTo", asset.AssignedTo ?? "" },
                { "AssignedUserId", asset.AssignedUserId?.ToString() ?? "" },
                { "Location", asset.Location ?? "" },
                { "Value", asset.Value?.ToString() ?? "" },
                { "Description", asset.Description ?? "" }
            };

            _context.Assets.Remove(asset);
            await _context.SaveChangesAsync();

            try
            {
                // 记录删除资产的活动日志
                await _activityLogService.LogCombinedAssetActivityAsync(
                    ActivityType.Delete,
                    new List<ActivityType>(), // 删除不需要次要活动类型
                    null, // 资产已删除，不传递ID
                    userId,
                    userName,
                    assetName,
                    assetNumber,
                    oldValues,
                    null // 删除后没有新值
                );
                
                Console.WriteLine($"Successfully logged asset deletion: {assetName} ({assetNumber}) by user {userName} (ID: {userId})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to log asset deletion activity: {ex.Message}");
                // 不抛出异常，因为资产已经删除成功
            }

            return true;
        }

        private (int userId, string userName) GetCurrentUserInfo()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = httpContext.User.FindFirst(ClaimTypes.NameIdentifier);
                var userNameClaim = httpContext.User.FindFirst(ClaimTypes.Name);
                
                Console.WriteLine($"Found claims - UserId: {userIdClaim?.Value}, UserName: {userNameClaim?.Value}");
                
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    return (userId, userNameClaim?.Value ?? "Unknown");
                }
            }
            
            Console.WriteLine("No authenticated user found, using default values");
            // 如果无法获取用户信息，返回默认值
            return (0, "System");
        }

        private static AssetDto MapToAssetDto(Asset asset)
        {
            return new AssetDto
            {
                Id = asset.Id,
                Name = asset.Name,
                AssetNumber = asset.AssetNumber,
                Category = asset.Category.ToString().ToLower(),
                Status = asset.Status.ToString().ToLower(),
                Brand = asset.Brand,
                Model = asset.Model,
                SerialNumber = asset.SerialNumber,
                AssignedTo = asset.AssignedTo,
                AssignedUserId = asset.AssignedUserId,
                PurchaseDate = asset.PurchaseDate,
                Value = asset.Value,
                Vendor = asset.Vendor,
                Description = asset.Description,
                Location = asset.Location,
                LastMaintenanceDate = asset.LastMaintenanceDate,
                NextMaintenanceDate = asset.NextMaintenanceDate,
                CreatedAt = asset.CreatedAt,
                UpdatedAt = asset.UpdatedAt
            };
        }
    }
} 