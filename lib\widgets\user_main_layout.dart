import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../config/routes.dart';

class UserMainLayout extends StatefulWidget {
  final Widget child;
  final String currentRoute;
  final String? title;
  final bool showBackButton;
  final Widget? floatingActionButton;

  const UserMainLayout({
    super.key,
    required this.child,
    required this.currentRoute,
    this.title,
    this.showBackButton = false,
    this.floatingActionButton,
  });

  @override
  State<UserMainLayout> createState() => _UserMainLayoutState();
}

class _UserMainLayoutState extends State<UserMainLayout> {
  int _getCurrentIndex() {
    switch (widget.currentRoute) {
      case AppRoutes.userDashboard:
        return 0;
      case AppRoutes.ticketList:
        return 1;
      case AppRoutes.userProfile:
        return 2;
      default:
        // 对于子页面，根据父页面确定索引
        if (widget.currentRoute.startsWith('/tickets')) {
          return 1; // 工单相关页面
        }
        return 0; // 默认仪表板
    }
  }

  void _onTabTapped(int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.userDashboard);
        break;
      case 1:
        context.go(AppRoutes.ticketList);
        break;
      case 2:
        context.go(AppRoutes.userProfile);
        break;
    }
  }

  String _getTitle() {
    if (widget.title != null) {
      return widget.title!;
    }
    
    switch (widget.currentRoute) {
      case AppRoutes.userDashboard:
        return '我的工作台';
      case AppRoutes.ticketList:
        return 'IT工单';
      case AppRoutes.userProfile:
        return '个人资料';
      default:
        if (widget.currentRoute.startsWith('/tickets')) {
          if (widget.currentRoute.contains('/new')) {
            return '创建工单';
          } else if (widget.currentRoute.contains('/tickets/') && !widget.currentRoute.contains('/new')) {
            return '工单详情';
          }
          return 'IT工单';
        }
        return '我的工作台';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle()),
        leading: widget.showBackButton
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  } else {
                    // 如果无法返回，根据当前页面跳转到合适的父页面
                    if (widget.currentRoute.startsWith('/tickets')) {
                      context.go(AppRoutes.ticketList);
                    } else {
                      context.go(AppRoutes.userDashboard);
                    }
                  }
                },
              )
            : null,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) async {
              switch (value) {
                case 'profile':
                  context.go(AppRoutes.userProfile);
                  break;
                case 'logout':
                  final authProvider = Provider.of<AuthProvider>(context, listen: false);
                  await authProvider.logout();
                  if (context.mounted) {
                    context.go(AppRoutes.login);
                  }
                  break;
              }
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem<String>(
                value: 'profile',
                child: ListTile(
                  leading: Icon(Icons.person),
                  title: Text('个人资料'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem<String>(
                value: 'logout',
                child: ListTile(
                  leading: Icon(Icons.logout),
                  title: Text('退出登录'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              child: CircleAvatar(
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                child: Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final user = authProvider.currentUser;
                    final displayName = user?.fullName ?? user?.username ?? 'U';
                    return Text(
                      displayName.isNotEmpty ? displayName[0].toUpperCase() : 'U',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
      body: widget.child,
      floatingActionButton: widget.floatingActionButton,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _getCurrentIndex(),
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: '工作台',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.support_agent),
            label: 'IT工单',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: '个人资料',
          ),
        ],
      ),
    );
  }
}
